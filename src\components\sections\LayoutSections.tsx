'use client';

import React, { useState, useEffect } from "react";
import Container from "../Container";
import ThemeToggle from "../ThemeToggle";

// Header Navigation Component
export function HeaderNav() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 w-full z-[9999] transition-all duration-500 ease-in-out ${
        isScrolled
          ? 'backdrop-blur-md supports-[backdrop-filter]:bg-background/90 bg-background/95 border-b border-secondary-button-border shadow-lg text-foreground'
          : 'bg-transparent text-foreground'
      }`}
    >
      <Container className={`flex items-center justify-between transition-all duration-500 ease-in-out ${
        isScrolled ? 'h-16 py-3' : 'h-20 py-5'
      }`}>
        <div className="font-heading font-semibold" role="banner">
          <a href="#hero" className="focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Brand - Go to homepage">
            Brand
          </a>
        </div>
        <nav className="hidden sm:flex items-center gap-6 text-sm" role="navigation" aria-label="Main navigation">
          <a href="#benefits" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to Benefits section">Benefits</a>
          <a href="#location" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to Location section">Lokasi</a>
          <a href="#bestsellers" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to Best Sellers section">Best Sellers</a>
          <a href="#testimonials" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to Testimonials section">Testimoni</a>
          <a href="#about" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to About Us section">Tentang Kami</a>
          <a href="#gallery" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to Gallery section">Galeri</a>
          <a href="#faq" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded transition-colors" aria-label="Go to FAQ section">FAQ</a>
        </nav>
        <div className="flex items-center gap-3">
          <ThemeToggle isScrolled={isScrolled} />
          <a
            href="#contact"
            className="inline-flex h-10 items-center justify-center rounded-md px-6 text-sm font-medium transition-all focus-visible:ring-2 focus-visible:ring-offset-2 bg-primary-button-bg text-primary-button-text hover:bg-primary-button-hover focus-visible:ring-primary-button-bg"
            aria-label="Go to Contact section"
          >
            Contact
          </a>
        </div>
      </Container>
    </header>
  );
}

// Site Footer Component
export function SiteFooter() {
  return (
    <footer className="border-t py-10 mt-10" role="contentinfo">
      <Container className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-muted-foreground-light">
          © {new Date().getFullYear()} Brand. All rights reserved.
        </div>
        <nav className="flex items-center gap-4 text-sm" role="navigation" aria-label="Footer navigation">
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded"
            href="#"
            aria-label="Privacy Policy"
          >
            Privacy
          </a>
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded"
            href="#"
            aria-label="Terms of Service"
          >
            Terms
          </a>
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 rounded"
            href="#contact"
            aria-label="Contact Us"
          >
            Contact
          </a>
        </nav>
      </Container>
    </footer>
  );
}
