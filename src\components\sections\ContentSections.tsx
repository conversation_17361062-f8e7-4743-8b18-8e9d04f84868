import React from "react";
import Container from "../Container";
import Map from "../Map";

// Hero Section Component
export function Hero() {
  return (
    <section id="hero" className="pt-28 pb-16 sm:pt-36 sm:pb-24 bg-background" aria-labelledby="hero-heading">
      <Container className="text-center">
        <div className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium gap-2" role="status" aria-label="New template announcement">
          <span className="inline-block h-2 w-2 rounded-full bg-emerald-500" aria-hidden="true" />
          New template • Next.js + Tailwind
        </div>
        <h1 id="hero-heading" className="mt-6 text-4xl sm:text-5xl md:text-6xl font-heading font-semibold tracking-tight">
          Jadikan brand Anda menonjol dengan landing page modern
        </h1>
        <p className="mt-4 text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto">
          Template responsif, cepat, dan mudah di<PERSON>tom<PERSON>. Cocok untuk bisnis, produk, atau kampanye.
        </p>
        <div className="mt-8 flex items-center justify-center gap-3" role="group" aria-label="Call to action buttons">
          <a
            href="#contact"
            className="inline-flex h-11 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-5 text-sm font-medium hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg"
            aria-label="Start using the template now"
          >
            Mulai Sekarang
          </a>
          <a
            href="#gallery"
            className="inline-flex h-11 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-5 text-sm font-medium hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
            aria-label="View template demo and examples"
          >
            Lihat Demo
          </a>
        </div>
      </Container>
    </section>
  );
}

// Key Benefits Section Component
const benefits = [
  {
    title: "Cepat & SEO-friendly",
    desc: "Dibangun dengan Next.js 15 dan optimasi modern.",
  },
  {
    title: "Mudah dikustomisasi",
    desc: "Tailwind CSS memudahkan styling yang konsisten.",
  },
  {
    title: "Responsif",
    desc: "Tampilan optimal di semua perangkat.",
  },
];

export function KeyBenefits() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Key Benefits</h2>
        <div className="mt-8 grid gap-6 sm:grid-cols-3">
          {benefits.map((b) => (
            <div key={b.title} className="rounded-xl border border-card-border p-6 bg-card-bg">
              <h3 className="font-heading font-semibold text-lg">{b.title}</h3>
              <p className="mt-2 text-sm text-muted-foreground">{b.desc}</p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

// About Section Component
const aboutItems = [
  {
    id: "about-misi",
    title: "Misi",
    content: "Membantu bisnis tumbuh lewat web yang cepat, indah, dan efektif."
  },
  {
    id: "about-moto",
    title: "Moto",
    content: "\"Sederhana, Cepat, Berkualitas\""
  },
  {
    id: "about-spesialitas",
    title: "Spesialitas",
    content: "Landing Page Modern • Optimasi SEO • Performa Tinggi"
  },
  {
    id: "about-pengalaman",
    title: "Pengalaman",
    content: "5+ tahun dalam pengembangan web dan desain UI/UX yang user-friendly."
  }
];

export function About() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Tentang Kami</h2>
        <div className="mt-6 grid gap-8 lg:grid-cols-3 items-start">
          {/* Left column - 1/3 width */}
          <div className="lg:col-span-1">
            <p className="text-sm text-muted-foreground leading-relaxed">
              Kami adalah tim yang fokus pada pengalaman pengguna dan performa.
              Template ini dirancang untuk memudahkan Anda meluncurkan landing page berkualitas tinggi
              dengan teknologi terdepan dan desain yang modern.
            </p>
          </div>

          {/* Right column - 2/3 width - Stacked items like FAQ */}
          <div className="lg:col-span-2 grid gap-4">
            {aboutItems.map((item) => (
              <div
                key={item.id}
                className="rounded-lg border p-4 group"
              >
                <h3 className="font-medium">
                  {item.title}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    {item.content}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
}

// Location & Hours Section Component
export function LocationHours() {
  // Jakarta coordinates for demo - replace with actual business location
  const businessLocation = {
    latitude: -6.2088,
    longitude: 106.8456,
    address: "Jl. Contoh No. 123, Jakarta, Indonesia"
  };

  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Lokasi & Jam Operasional</h2>

        {/* Information Section */}
        <div className="mt-6 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div className="rounded-xl border border-card-border p-6 bg-card-bg">
            <h3 className="font-heading font-semibold text-primary">Alamat</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {businessLocation.address}
            </p>
          </div>

          <div className="rounded-xl border border-card-border p-6 bg-card-bg">
            <h3 className="font-heading font-semibold text-primary">Jam Operasional</h3>
            <ul className="mt-2 text-sm text-muted-foreground space-y-1">
              <li>Senin - Jumat: 09:00 - 18:00</li>
              <li>Sabtu: 10:00 - 16:00</li>
              <li>Minggu & Hari Libur: Tutup</li>
            </ul>
          </div>

          <div className="rounded-xl border border-card-border p-6 bg-card-bg sm:col-span-2 lg:col-span-1">
            <h3 className="font-heading font-semibold text-primary">Kontak</h3>
            <div className="mt-2 text-sm text-muted-foreground space-y-1">
              <p>📞 +62 21 1234 5678</p>
              <p>✉️ <EMAIL></p>
              <div className="mt-3">
                <a
                  href={`https://www.google.com/maps?q=${businessLocation.latitude},${businessLocation.longitude}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex h-9 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-3 text-xs hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
                >
                  Lihat di Google Maps
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-8">
          <Map
            latitude={businessLocation.latitude}
            longitude={businessLocation.longitude}
            address={businessLocation.address}
            className="w-full h-[400px] rounded-xl overflow-hidden border border-card-border"
          />
        </div>
      </Container>
    </section>
  );
}
