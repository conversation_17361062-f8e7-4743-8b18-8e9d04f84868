@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --muted-foreground: #525252; /* zinc-600 equivalent but darker for better contrast */
  --muted-foreground-light: #737373; /* zinc-500 for less important text */

  /* Button colors */
  --primary-button-bg: #171717; /* black for light mode */
  --primary-button-text: #ffffff;
  --primary-button-hover: #262626; /* zinc-800 */

  --secondary-button-bg: transparent;
  --secondary-button-text: var(--foreground);
  --secondary-button-border: #e4e4e7; /* zinc-200 */
  --secondary-button-hover: #f4f4f5; /* zinc-100 */

  /* Card colors */
  --card-bg: #f9fafb; /* gray-50 for light mode */
  --card-border: #e4e4e7; /* zinc-200 */
}

:root.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --muted-foreground: #a1a1aa; /* zinc-400 for dark theme */
  --muted-foreground-light: #71717a; /* zinc-500 for dark theme */

  /* Button colors for dark mode */
  --primary-button-bg: #ffffff; /* white for dark mode */
  --primary-button-text: #171717;
  --primary-button-hover: #f4f4f5; /* zinc-100 */

  --secondary-button-bg: transparent;
  --secondary-button-text: var(--foreground);
  --secondary-button-border: #27272a; /* zinc-800 */
  --secondary-button-hover: #18181b; /* zinc-900 */

  /* Card colors for dark mode */
  --card-bg: #18181b; /* zinc-900 for dark mode */
  --card-border: #27272a; /* zinc-800 */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted-foreground-light: var(--muted-foreground-light);

  --color-primary-button-bg: var(--primary-button-bg);
  --color-primary-button-text: var(--primary-button-text);
  --color-primary-button-hover: var(--primary-button-hover);

  --color-secondary-button-bg: var(--secondary-button-bg);
  --color-secondary-button-text: var(--secondary-button-text);
  --color-secondary-button-border: var(--secondary-button-border);
  --color-secondary-button-hover: var(--secondary-button-hover);

  --color-card-bg: var(--card-bg);
  --color-card-border: var(--card-border);

  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

body {
  font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background: var(--background);
  color: var(--foreground);
  transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Header scroll effects */
.header-scrolled {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --muted-foreground: #000000;
    --muted-foreground-light: #000000;
  }

  :root.dark {
    --muted-foreground: #ffffff;
    --muted-foreground-light: #ffffff;
  }
}
