import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)'],
        heading: ['var(--font-poppins)'],
      },
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        'muted-foreground': 'var(--muted-foreground)',
        'muted-foreground-light': 'var(--muted-foreground-light)',

        // Button colors
        'primary-button': {
          bg: 'var(--primary-button-bg)',
          text: 'var(--primary-button-text)',
          hover: 'var(--primary-button-hover)',
        },
        'secondary-button': {
          bg: 'var(--secondary-button-bg)',
          text: 'var(--secondary-button-text)',
          border: 'var(--secondary-button-border)',
          hover: 'var(--secondary-button-hover)',
        },

        // Card colors
        'card': {
          bg: 'var(--card-bg)',
          border: 'var(--card-border)',
        },
      },
    },
  },
  plugins: [],
}

export default config
