'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Validate theme value to prevent XSS
const isValidTheme = (value: string): value is Theme => {
  return value === 'light' || value === 'dark';
};

// Safe localStorage access with error handling
const getStoredTheme = (): Theme | null => {
  try {
    if (typeof window === 'undefined') return null;
    const stored = localStorage.getItem('theme');
    return stored && isValidTheme(stored) ? stored : null;
  } catch (error) {
    console.warn('Failed to access localStorage for theme:', error);
    return null;
  }
};

const setStoredTheme = (theme: Theme): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', theme);
    }
  } catch (error) {
    console.warn('Failed to save theme to localStorage:', error);
  }
};

// Get system theme preference
const getSystemTheme = (): Theme => {
  try {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  } catch (error) {
    console.warn('Failed to detect system theme:', error);
    return 'light';
  }
};

// Get initial theme from server script
const getInitialTheme = (): Theme => {
  try {
    if (typeof window === 'undefined') return 'light';
    // Check if the server script set an initial theme
    const initialTheme = (window as any).__INITIAL_THEME__;
    if (initialTheme && isValidTheme(initialTheme)) {
      return initialTheme;
    }
    // Fallback to stored theme or system theme
    return getStoredTheme() || getSystemTheme();
  } catch (error) {
    console.warn('Failed to get initial theme:', error);
    return 'light';
  }
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);

  // Initialize theme on mount
  useEffect(() => {
    const initialTheme = getInitialTheme();

    setThemeState(initialTheme);

    // Set the theme class immediately to prevent FOUC
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(initialTheme);

    setMounted(true);

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      // Only update if no saved preference exists
      if (!getStoredTheme()) {
        setThemeState(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, []);

  // Update document class and localStorage when theme changes (after initial mount)
  useEffect(() => {
    if (mounted) {
      const root = document.documentElement;

      // Use requestAnimationFrame for smoother transitions
      requestAnimationFrame(() => {
        root.classList.remove('light', 'dark');
        root.classList.add(theme);
      });

      setStoredTheme(theme);
    }
  }, [theme, mounted]);

  // Memoized callbacks to prevent unnecessary re-renders
  const toggleTheme = useCallback(() => {
    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  }, []);

  const setTheme = useCallback((newTheme: Theme) => {
    if (isValidTheme(newTheme)) {
      setThemeState(newTheme);
    } else {
      console.warn('Invalid theme value:', newTheme);
    }
  }, []);

  // Memoized context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    theme,
    toggleTheme,
    setTheme,
    isLoading: !mounted,
  }), [theme, toggleTheme, setTheme, mounted]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
