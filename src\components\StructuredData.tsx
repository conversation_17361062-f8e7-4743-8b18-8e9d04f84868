import React from 'react';
import { baseUrl } from '@/lib/env';

interface StructuredDataProps {
  data: object;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
    />
  );
}

// Website structured data
export const websiteStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Landing Template",
  "description": "Template landing page modern yang dibangun dengan Next.js 15 dan Tailwind CSS. Responsif, cepat, SEO-friendly, dan mudah dikustomisasi untuk bisnis Anda.",
  "url": baseUrl,
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${baseUrl}/?q={search_term_string}`
    },
    "query-input": "required name=search_term_string"
  }
};

// Organization structured data
export const organizationStructuredData = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Landing Template",
  "description": "Modern landing page templates for businesses",
  "url": baseUrl,
  "logo": `${baseUrl}/favicon.svg`,
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "availableLanguage": ["Indonesian", "English"]
  }
};

// FAQ structured data
export const faqStructuredData = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Apakah bisa di-deploy ke Vercel?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Ya, Next.js sangat cocok untuk Vercel dan dapat di-deploy dengan mudah."
      }
    },
    {
      "@type": "Question", 
      "name": "Apakah mudah diubah?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Semua komponen dibuat modular dan mudah dikustomisasi sesuai kebutuhan brand Anda."
      }
    },
    {
      "@type": "Question",
      "name": "Apakah SEO-friendly?", 
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Iya, template ini dibangun dengan meta tag lengkap dan praktik SEO Next.js modern."
      }
    }
  ]
};
